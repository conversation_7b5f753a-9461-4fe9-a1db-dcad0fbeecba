import { MigrationInterface, QueryRunner } from 'typeorm'

export class MakeCrmUuidNotNull1753873576370 implements MigrationInterface {
  name = 'MakeCrmUuidNotNull1753873576370'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "care_user" ALTER COLUMN "crm_individual_uuid" SET NOT NULL`)
    await queryRunner.query(`ALTER TABLE "organization" ALTER COLUMN "crm_business_uuid" SET NOT NULL`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "organization" ALTER COLUMN "crm_business_uuid" DROP NOT NULL`)
    await queryRunner.query(`ALTER TABLE "care_user" ALTER COLUMN "crm_individual_uuid" DROP NOT NULL`)
  }
}

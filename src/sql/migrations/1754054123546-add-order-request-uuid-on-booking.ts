import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddOrderRequestUuidOnBooking1754054123546 implements MigrationInterface {
  name = 'AddOrderRequestUuidOnBooking1754054123546'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "booking" ADD "order_request_uuid" uuid NOT NULL`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "UQ_7780308a9f5355d74a26f95bdba" UNIQUE ("order_request_uuid")`)
    await queryRunner.query(`ALTER TABLE "booking" ADD CONSTRAINT "FK_7780308a9f5355d74a26f95bdba" FOREIGN KEY ("order_request_uuid") REFERENCES "order_request"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "FK_7780308a9f5355d74a26f95bdba"`)
    await queryRunner.query(`ALTER TABLE "booking" DROP CONSTRAINT "UQ_7780308a9f5355d74a26f95bdba"`)
    await queryRunner.query(`ALTER TABLE "booking" DROP COLUMN "order_request_uuid"`)
  }
}

import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import jwt from 'jsonwebtoken'
import dayjs from 'dayjs'
import axios from 'axios'
import { captureException } from '@sentry/nestjs'
import { IndividualResponse } from './types/individual-response.type.js'
import { BusinessResponse } from './types/business-response.type.js'

type ZitadelUserKey = {
  type: string
  keyId: string
  key: string
  expirationDate: string
  userId: string
}

interface TokenResponse {
  access_token: string
  token_type: string
  expires_in: number
  scope: string
}

@Injectable()
export class CrmClient {
  private readonly issuer: string
  private readonly crmBaseUrl: string
  constructor (
    private readonly configService: ConfigService
  ) {
    this.issuer = this.configService.getOrThrow<string>('AUTH_CRM_ISSUER')
    this.crmBaseUrl = this.configService.getOrThrow<string>('CRM_BASE_URL')
  }

  async getIndividualOrFail (crmIndividualUuid: string): Promise<IndividualResponse> {
    const token = await this.getToken()

    const crmIndividualUrl = `${this.crmBaseUrl}/api/v1/individuals/${crmIndividualUuid}`

    try {
      const response = await axios.get<IndividualResponse>(crmIndividualUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'User-Type': 'serviceaccount'
        }
      })

      return response.data
    } catch (error) {
      captureException(`Failed to fetch individual from CRM: ${JSON.stringify(error)}`)

      throw new Error('Failed to fetch individual from CRM')
    }
  }

  async getBusinessOrFail (crmBusinessUuid: string): Promise<BusinessResponse> {
    const token = await this.getToken()

    const crmBusinessUrl = `${this.crmBaseUrl}/api/v1/businesses/${crmBusinessUuid}`

    try {
      const response = await axios.get<BusinessResponse>(crmBusinessUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'User-Type': 'serviceaccount'
        }
      })

      return response.data
    } catch (error) {
      captureException(`Failed to fetch business from CRM: ${JSON.stringify(error)}`)

      throw new Error('Failed to fetch business from CRM')
    }
  }

  private getJwt (): string {
    const encodedServiceUserKey = this.configService.getOrThrow<string>('AUTH_CRM_SERVICE_USER_KEY')
    const serviceUserKey: ZitadelUserKey
    = JSON.parse(Buffer.from(encodedServiceUserKey, 'base64').toString('utf-8')) as ZitadelUserKey

    return jwt.sign(
      {
        iss: serviceUserKey.userId,
        sub: serviceUserKey.userId,
        aud: this.issuer,
        exp: dayjs().add(1, 'hour').unix(),
        iat: dayjs().unix()
      },
      serviceUserKey.key,
      {
        algorithm: 'RS256',
        keyid: serviceUserKey.keyId
      }
    )
  }

  private async getToken (): Promise<string> {
    const authCrmTokenUrl = `${this.issuer}/oauth/v2/token`
    const authCrmProjectId = this.configService.getOrThrow<string>('AUTH_CRM_PROJECT_ID')

    const scopes: string[] = [
      `urn:zitadel:iam:org:project:id:${authCrmProjectId}:aud`,
      'urn:zitadel:iam:user:resourceowner'
    ]

    const jwt = this.getJwt()

    try {
      const response = await axios.post(
        authCrmTokenUrl,
        new URLSearchParams({
          grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          assertion: jwt,
          scope: scopes.join(' ')
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      return (response.data as TokenResponse).access_token
    } catch (error) {
      captureException(`Failed to exchange JWT for access token: ${JSON.stringify(error)}`)

      throw new Error('Token exchange failed')
    }
  }
}

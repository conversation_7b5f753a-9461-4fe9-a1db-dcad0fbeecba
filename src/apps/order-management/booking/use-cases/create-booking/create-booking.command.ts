import { WiseDate } from '@wisemen/wise-date'
import { UserUuid } from '../../../../../app/users/entities/user.uuid.js'
import { ClientType } from '../../../client/client-type.js'

export class CreateBookingCommand {
  clientUuid: string
  clientType: ClientType
  isRecurring: boolean
  startDate: WiseDate | null
  iterationIntervalWeeks: number | null
  remarksForDriver: string | null
  remarksForPlanner: string | null
  contractUuid: string
  careUserUuid: string
  createdByUserUuid: UserUuid
  updatedByUserUuid: UserUuid
}

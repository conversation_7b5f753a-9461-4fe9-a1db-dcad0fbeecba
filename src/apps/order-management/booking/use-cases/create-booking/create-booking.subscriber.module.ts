import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Booking } from '../../entities/booking.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { CreateBookingSubscriber } from './create-booking.subscriber.js'
import { CreateBookingUseCase } from './create-booking.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Booking]),
    DomainEventEmitterModule
  ],
  providers: [
    CreateBookingSubscriber,
    CreateBookingUseCase
  ]
})
export class CreateBookingSubscriberModule {}

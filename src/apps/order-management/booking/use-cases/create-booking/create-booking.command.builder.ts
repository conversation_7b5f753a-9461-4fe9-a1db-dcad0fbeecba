import { WiseDate } from '@wisemen/wise-date'
import { UserUuid } from '../../../../../app/users/entities/user.uuid.js'
import { ClientType } from '../../../client/client-type.js'
import { CreateBookingCommand } from './create-booking.command.js'

export class CreateBookingCommandBuilder {
  private command: CreateBookingCommand

  constructor () {
    this.command = new CreateBookingCommand()
  }

  withClientUuid (clientUuid: string): this {
    this.command.clientUuid = clientUuid
    return this
  }

  withClientType (clientType: ClientType): this {
    this.command.clientType = clientType
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.command.isRecurring = isRecurring
    return this
  }

  withStartDate (startDate: WiseDate | null): this {
    this.command.startDate = startDate
    return this
  }

  withIterationIntervalWeeks (iterationIntervalWeeks: number | null): this {
    this.command.iterationIntervalWeeks = iterationIntervalWeeks
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.command.remarksForDriver = remarksForDriver
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.command.remarksForPlanner = remarksForPlanner
    return this
  }

  withContractUuid (contractUuid: string): this {
    this.command.contractUuid = contractUuid
    return this
  }

  withCareUserUuid (careUserUuid: string): this {
    this.command.careUserUuid = careUserUuid
    return this
  }

  withCreatedByUserUuid (createdByUserUuid: UserUuid): this {
    this.command.createdByUserUuid = createdByUserUuid
    return this
  }

  withUpdatedByUserUuid (updatedByUserUuid: UserUuid): this {
    this.command.updatedByUserUuid = updatedByUserUuid
    return this
  }

  build (): CreateBookingCommand {
    return this.command
  }
}

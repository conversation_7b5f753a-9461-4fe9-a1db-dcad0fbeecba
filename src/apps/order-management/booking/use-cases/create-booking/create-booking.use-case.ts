import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { BookingEntityBuilder } from '../../entities/booking.entity.builder.js'
import { Booking } from '../../entities/booking.entity.js'
import { CreateBookingCommand } from './create-booking.command.js'
import { BookingCreatedEvent } from './booking-created.event.js'

@Injectable()
export class CreateBookingUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Booking)
    private readonly bookingRepository: Repository<Booking>
  ) {}

  async execute (
    orderRequestUuid: string,
    CreateBookingCommand: CreateBookingCommand
  ): Promise<void> {
    const booking = new BookingEntityBuilder()
      .withClientUuid(CreateBookingCommand.clientUuid)
      .withClientType(CreateBookingCommand.clientType)
      .withIsRecurring(CreateBookingCommand.isRecurring)
      .withStartDate(CreateBookingCommand.startDate)
      .withIterationIntervalWeeks(CreateBookingCommand.iterationIntervalWeeks)
      .withRemarksForDriver(CreateBookingCommand.remarksForDriver)
      .withRemarksForPlanner(CreateBookingCommand.remarksForPlanner)
      .withContractUuid(CreateBookingCommand.contractUuid)
      .withCareUserUuid(CreateBookingCommand.careUserUuid)
      .withCreatedByUserUuid(CreateBookingCommand.createdByUserUuid)
      .withUpdatedByUserUuid(CreateBookingCommand.updatedByUserUuid)
      .withOrderRequestUuid(orderRequestUuid)
      .build()

    await transaction(this.dataSource, async () => {
      await this.bookingRepository.insert(booking)
      await this.eventEmitter.emitOne(new BookingCreatedEvent(booking.uuid))
    })
  }
}

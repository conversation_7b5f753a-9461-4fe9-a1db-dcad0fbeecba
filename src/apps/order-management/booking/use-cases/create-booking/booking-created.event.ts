import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { BookingUuid } from '../../entities/booking.uuid.js'

@OneOfMeta(DomainEventLog, DomainEventType.BOOKING_CREATED)
export class BookingCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly bookingUuid: BookingUuid

  constructor (bookingUuid: BookingUuid) {
    this.bookingUuid = bookingUuid
  }
}

@RegisterDomainEvent(DomainEventType.BOOKING_CREATED, 1)
export class BookingCreatedEvent extends DomainEvent<BookingCreatedEventContent> {
  constructor (bookingUuid: BookingUuid) {
    super({ content: new BookingCreatedEventContent(bookingUuid) })
  }
}

import { Injectable } from '@nestjs/common'
import { Subscribe } from '../../../../../modules/domain-events/subscribe.decorator.js'
import { OrderRequestAcceptedEvent } from '../../../order-request/use-cases/accept-order-request/order-request-accepted.event.js'
import { CreateBookingCommandBuilder } from './create-booking.command.builder.js'
import { CreateBookingUseCase } from './create-booking.use-case.js'

@Injectable()
export class CreateBookingSubscriber {
  constructor (
    private readonly createBookingUseCase: CreateBookingUseCase
  ) {}

  @Subscribe(OrderRequestAcceptedEvent)
  async onOrderRequestAccepted (events: OrderRequestAcceptedEvent[]): Promise<void> {
    for (const event of events) {
      const command = new CreateBookingCommandBuilder()
        .withClientUuid(event.content.clientUuid)
        .withClientType(event.content.clientType)
        .withIsRecurring(event.content.isRecurring)
        .withStartDate(event.content.startDate)
        .withIterationIntervalWeeks(event.content.iterationIntervalWeeks)
        .withRemarksForDriver(event.content.remarksForDriver)
        .withRemarksForPlanner(event.content.remarksForPlanner)
        .withContractUuid(event.content.contractUuid)
        .withCareUserUuid(event.content.careUserUuid)
        .withCreatedByUserUuid(event.content.createdByUserUuid)
        .withUpdatedByUserUuid(event.content.updatedByUserUuid)
        .build()

      await this.createBookingUseCase.execute(event.content.orderRequestUuid, command)
    }
  }
}

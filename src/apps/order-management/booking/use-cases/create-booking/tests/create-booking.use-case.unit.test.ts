import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { WiseDate } from '@wisemen/wise-date'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { generateUserUuid } from '../../../../../../app/users/entities/user.uuid.js'
import { ClientType } from '../../../../client/client-type.js'
import { Booking } from '../../../entities/booking.entity.js'
import { CreateBookingUseCase } from '../create-booking.use-case.js'
import { CreateBookingCommandBuilder } from '../create-booking.command.builder.js'
import { BookingCreatedEvent } from '../booking-created.event.js'

describe('CreateBookingUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('successfully creates a booking and emits event', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const bookingRepository = createStubInstance(Repository<Booking>)

    bookingRepository.insert.resolves()

    const useCase = new CreateBookingUseCase(
      stubDataSource(),
      eventEmitter,
      bookingRepository
    )

    const orderRequestUuid = randomUUID()
    const command = new CreateBookingCommandBuilder()
      .withClientUuid(randomUUID())
      .withClientType(ClientType.ORGANIZATION)
      .withIsRecurring(false)
      .withStartDate(new WiseDate('2024-12-01'))
      .withIterationIntervalWeeks(null)
      .withRemarksForDriver('Test driver remarks')
      .withRemarksForPlanner('Test planner remarks')
      .withContractUuid(randomUUID())
      .withCareUserUuid(randomUUID())
      .withCreatedByUserUuid(generateUserUuid())
      .withUpdatedByUserUuid(generateUserUuid())
      .build()

    await useCase.execute(orderRequestUuid, command)

    expect(bookingRepository.insert.calledOnce).toBe(true)
    const insertedBooking = bookingRepository.insert.getCall(0).args[0] as Booking
    expect(eventEmitter).toHaveEmitted(new BookingCreatedEvent(insertedBooking.uuid))
  })
})

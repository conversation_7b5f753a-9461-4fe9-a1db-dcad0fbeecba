import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { ViewBookingIndexRepository } from './view-booking-index.repository.js'
import { ViewBookingIndexQuery } from './view-booking-index.query.js'
import { ViewBookingIndexResponse } from './view-booking-index.response.js'

@Injectable()
export class ViewBookingIndexUseCase {
  constructor (
    private readonly repository: ViewBookingIndexRepository
  ) {}

  async execute (query: ViewBookingIndexQuery): Promise<ViewBookingIndexResponse> {
    const pagination = typeormPagination(query.pagination)
    const [bookings, totalCount] = await this.repository.findBookings(query)

    return new ViewBookingIndexResponse(
      bookings,
      totalCount,
      pagination.take,
      pagination.skip
    )
  }
}

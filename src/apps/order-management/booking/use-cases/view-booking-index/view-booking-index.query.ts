import {
  ArrayMinSize,
  Equals,
  <PERSON><PERSON>rray,
  IsObject,
  IsOptional,
  ValidateNested
} from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ViewBookingIndexSortQuery } from './view-booking-index.sort-query.js'
import { ViewBookingIndexFilterQuery } from './view-booking-index-filter.query.js'

export class ViewBookingIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewBookingIndexSortQuery, isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsObject({ each: true })
  @Type(() => ViewBookingIndexSortQuery)
  @ValidateNested({ each: true })
  sort?: ViewBookingIndexSortQuery[]

  @ApiProperty({ type: ViewBookingIndexFilterQuery, required: false })
  @IsOptional()
  @IsObject()
  @Type(() => ViewBookingIndexFilterQuery)
  @ValidateNested()
  filter?: ViewBookingIndexFilterQuery

  @Equals(undefined)
  search?: never
}

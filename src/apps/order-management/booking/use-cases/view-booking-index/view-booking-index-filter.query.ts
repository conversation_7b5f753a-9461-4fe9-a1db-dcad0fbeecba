import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsOptional, IsUUID } from 'class-validator'
import { IsQueryBoolean } from '@wisemen/validators'
import { FilterQuery } from '@wisemen/pagination'
import { BookingStatus } from '../../enums/booking-status.js'

export class ViewBookingIndexFilterQuery extends FilterQuery {
  @ApiProperty({ enum: BookingStatus, required: false })
  @IsOptional()
  @IsEnum(BookingStatus)
  status?: BookingStatus

  @ApiProperty({ type: String, format: 'uuid', required: false })
  @IsOptional()
  @IsUUID()
  clientUuid?: string

  @ApiProperty({ type: String, format: 'uuid', required: false })
  @IsOptional()
  @IsUUID()
  contractUuid?: string

  @ApiProperty({ type: 'boolean', required: false })
  @IsOptional()
  @IsQueryBoolean()
  isRecurring?: string
}

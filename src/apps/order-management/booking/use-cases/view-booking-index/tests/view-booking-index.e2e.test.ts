import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { stringify } from 'qs'
import { expect } from 'expect'
import { WiseDate } from '@wisemen/wise-date'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { ViewBookingIndexQueryBuilder } from '../view-booking-index.query-builder.js'
import { BookingEntityBuilder } from '../../../entities/booking.entity.builder.js'
import { Booking } from '../../../entities/booking.entity.js'
import { BookingStatus } from '../../../enums/booking-status.js'
import { ViewBookingIndexSortQuery<PERSON>ey } from '../view-booking-index.sort-query.js'
import { SortDirection } from '../../../../../../utils/query/search.query.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { Organization } from '../../../../organization/entities/organization.entity.js'
import { ClientType } from '../../../../client/client-type.js'
import { ClientId } from '../../../../client/client-id.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { OrganizationEntityBuilder } from '../../../../organization/builders/organization.entity.builder.js'
import { OrderRequestEntityBuilder } from '../../../../order-request/entities/order-request.entity.builder.js'
import { OrderRequestStatus } from '../../../../order-request/enums/order-request-status.js'
import { OrderRequest } from '../../../../order-request/entities/order-request.entity.js'

describe('ViewBookingIndex E2E tests', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser
  let user1: User
  let user2: User
  let careUser: CareUser
  let organization: Organization
  let contractType: ContractType
  let contract1: Contract
  let contract2: Contract
  let booking1: Booking
  let booking2: Booking
  let booking3: Booking

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    // Create test users
    user1 = new UserEntityBuilder()
      .withFirstName('John')
      .withLastName('Doe')
      .build()

    user2 = new UserEntityBuilder()
      .withFirstName('Jane')
      .withLastName('Smith')
      .build()

    await setup.dataSource.manager.insert(User, [user1, user2])

    // Create care user and organization
    careUser = new CareUserEntityBuilder()
      .withName('Test Care User')
      .build()

    organization = new OrganizationEntityBuilder()
      .withName('Test Organization')
      .build()

    await setup.dataSource.manager.insert(CareUser, careUser)
    await setup.dataSource.manager.insert(Organization, organization)

    // Create pricing formula and max time formula
    const pricingFormula = new PricingFormulaEntityBuilder().build()
    const maxTimeFormula = new MaxTimeInVehicleFormulaEntityBuilder().build()

    await setup.dataSource.manager.insert(PricingFormula, pricingFormula)
    await setup.dataSource.manager.insert(MaxTimeInVehicleFormula, maxTimeFormula)

    // Create contract type
    contractType = new ContractTypeEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withMaxTimeInVehicleFormulaUuid(maxTimeFormula.uuid)
      .build()

    await setup.dataSource.manager.insert(ContractType, contractType)

    // Create contracts
    contract1 = new ContractEntityBuilder()
      .withContractTypeUuid(contractType.uuid)
      .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
      .build()

    contract2 = new ContractEntityBuilder()
      .withContractTypeUuid(contractType.uuid)
      .withClientId(new ClientId(organization.uuid, ClientType.ORGANIZATION))
      .build()

    await setup.dataSource.manager.insert(Contract, [contract1, contract2])

    const orderRequest1 = new OrderRequestEntityBuilder()
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withContractUuid(contract1.uuid)
      .withCareUserUuid(careUser.uuid)
      .withStatus(OrderRequestStatus.ACCEPTED)
      .withCreatedByUserUuid(user1.uuid)
      .withUpdatedByUserUuid(user1.uuid)
      .build()

    const orderRequest2 = new OrderRequestEntityBuilder()
      .withClientUuid(organization.uuid)
      .withClientType(ClientType.ORGANIZATION)
      .withContractUuid(contract2.uuid)
      .withCareUserUuid(careUser.uuid)
      .withStatus(OrderRequestStatus.ACCEPTED)
      .withCreatedByUserUuid(user1.uuid)
      .withUpdatedByUserUuid(user1.uuid)
      .build()

    const orderRequest3 = new OrderRequestEntityBuilder()
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withContractUuid(contract1.uuid)
      .withCareUserUuid(careUser.uuid)
      .withStatus(OrderRequestStatus.ACCEPTED)
      .withCreatedByUserUuid(user1.uuid)
      .withUpdatedByUserUuid(user1.uuid)
      .build()

    await setup.dataSource.manager.insert(
      OrderRequest,
      [orderRequest1, orderRequest2, orderRequest3]
    )

    // Create test bookings
    booking1 = new BookingEntityBuilder()
      .withStatus(BookingStatus.ACTIVE)
      .withClientUuid(careUser.uuid)
      .withCareUserUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withContractUuid(contract1.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(user1.uuid)
      .withUpdatedByUserUuid(user1.uuid)
      .withIsRecurring(false)
      .withStartDate(WiseDate.today().add(1, 'day'))
      .withOrderRequestUuid(orderRequest1.uuid)
      .build()

    booking2 = new BookingEntityBuilder()
      .withStatus(BookingStatus.COMPLETED)
      .withClientUuid(organization.uuid)
      .withClientType(ClientType.ORGANIZATION)
      .withContractUuid(contract2.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(user2.uuid)
      .withUpdatedByUserUuid(user2.uuid)
      .withIsRecurring(true)
      .withStartDate(WiseDate.today().add(2, 'days'))
      .withOrderRequestUuid(orderRequest2.uuid)
      .build()

    booking3 = new BookingEntityBuilder()
      .withStatus(BookingStatus.CANCELLED)
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withContractUuid(contract1.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(user1.uuid)
      .withUpdatedByUserUuid(user2.uuid)
      .withIsRecurring(false)
      .withStartDate(WiseDate.today().add(3, 'days'))
      .withOrderRequestUuid(orderRequest3.uuid)
      .build()

    await setup.dataSource.manager.insert(Booking, [booking1, booking2, booking3])
  })

  after(async () => await setup.teardown())

  it('returns bookings in a paginated format', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(3)
    expect(response.body.meta.total).toBe(3)
    expect(response.body.meta.limit).toBe(10)
    expect(response.body.meta.offset).toBe(0)
  })

  it('filters by status correctly', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withStatusFilter(BookingStatus.ACTIVE)
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(1)
    expect(response.body.items[0].uuid).toBe(booking1.uuid)
    expect(response.body.items[0].status).toBe(BookingStatus.ACTIVE)
  })

  it('filters by clientUuid correctly', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withClientUuidFilter(organization.uuid)
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(1)
    expect(response.body.items[0].uuid).toBe(booking2.uuid)
    expect(response.body.items[0].client.uuid).toBe(organization.uuid)
  })

  it('filters by contractUuid correctly', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withContractUuidFilter(contract2.uuid)
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(1)
    expect(response.body.items[0].uuid).toBe(booking2.uuid)
    expect(response.body.items[0].contract.uuid).toBe(contract2.uuid)
  })

  it('filters by isRecurring correctly', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withIsRecurringFilter(true)
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(1)
    expect(response.body.items[0].uuid).toBe(booking2.uuid)
    expect(response.body.items[0].isRecurring).toBe(true)
  })

  it('sorts by status ascending', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withSort(ViewBookingIndexSortQueryKey.STATUS, SortDirection.ASC)
      .withPagination({ limit: 10, offset: 0 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(3)
    expect(response.body.items[0].status).toBe(BookingStatus.ACTIVE)
    expect(response.body.items[1].status).toBe(BookingStatus.CANCELLED)
    expect(response.body.items[2].status).toBe(BookingStatus.COMPLETED)
  })

  it('includes all related entity information', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withStatusFilter(BookingStatus.ACTIVE)
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(1)

    expect(response.body.items[0]).toStrictEqual(expect.objectContaining({
      uuid: booking1.uuid,
      status: BookingStatus.ACTIVE,
      isRecurring: false,
      client: expect.objectContaining({
        uuid: careUser.uuid,
        type: ClientType.CARE_USER,
        name: careUser.name
      }),
      contract: expect.objectContaining({
        uuid: contract1.uuid,
        contractType: expect.objectContaining({
          uuid: contractType.uuid
        })
      }),
      careUser: expect.objectContaining({
        uuid: careUser.uuid
      }),
      createdByUser: expect.objectContaining({
        uuid: user1.uuid,
        firstName: user1.firstName,
        lastName: user1.lastName
      }),
      updatedByUser: expect.objectContaining({
        uuid: user1.uuid
      })
    }))
  })

  it('handles pagination correctly', async () => {
    const query = new ViewBookingIndexQueryBuilder()
      .withPagination({ limit: 2, offset: 1 })
      .build()

    const response = await request(setup.httpServer)
      .get('/bookings')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(2)
    expect(response.body.meta.total).toBe(3)
    expect(response.body.meta.limit).toBe(2)
    expect(response.body.meta.offset).toBe(1)
  })
})

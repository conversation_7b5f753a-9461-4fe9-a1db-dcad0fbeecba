import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository, SelectQueryBuilder } from 'typeorm'
import { SortDirection, typeormPagination } from '@wisemen/pagination'
import { Booking } from '../../entities/booking.entity.js'
import { ClientRepository } from '../../../client/client-repository.js'
import { ClientNotFoundError } from '../../../client/errors/client-not-found.error.js'
import { ClientId } from '../../../client/client-id.js'
import { ViewBookingIndexQuery } from './view-booking-index.query.js'
import { ViewBookingIndexSortQueryKey } from './view-booking-index.sort-query.js'

@Injectable()
export class ViewBookingIndexRepository {
  constructor (
    @InjectRepository(Booking)
    private readonly bookingRepository: Repository<Booking>,
    private readonly clientRepo: ClientRepository
  ) {}

  async findBookings (query: ViewBookingIndexQuery): Promise<[Booking[], number]> {
    const pagination = typeormPagination(query.pagination)

    const queryBuilder = this.bookingRepository.createQueryBuilder('booking')
      .leftJoinAndSelect('booking.contract', 'contract')
      .leftJoinAndSelect('contract.contractType', 'contractType')
      .leftJoinAndSelect('booking.careUser', 'careUser')
      .leftJoinAndSelect('booking.createdByUser', 'createdByUser')
      .leftJoinAndSelect('booking.updatedByUser', 'updatedByUser')

    this.applyFiltering(queryBuilder, query)
    this.applySorting(queryBuilder, query)

    const [items, totalCount] = await queryBuilder
      .skip(pagination.skip)
      .take(pagination.take)
      .getManyAndCount()

    const clientIds = items.map(booking => new ClientId(booking.clientUuid, booking.clientType))
    const clients = await this.clientRepo.findByIds(clientIds)

    for (const booking of items) {
      const client = clients.find(client =>
        client.hasId(new ClientId(booking.clientUuid, booking.clientType))
      )
      assert(client !== undefined, new ClientNotFoundError())
      booking.client = client
    }

    return [items, totalCount]
  }

  private applyFiltering (
    queryBuilder: SelectQueryBuilder<Booking>,
    query: ViewBookingIndexQuery
  ): void {
    if (query.filter?.status != null) {
      queryBuilder.andWhere('booking.status = :status', { status: query.filter.status })
    }

    if (query.filter?.clientUuid != null) {
      queryBuilder.andWhere('booking.clientUuid = :clientUuid', { clientUuid: query.filter.clientUuid })
    }

    if (query.filter?.contractUuid != null) {
      queryBuilder.andWhere('booking.contractUuid = :contractUuid', { contractUuid: query.filter.contractUuid })
    }

    if (query.filter?.isRecurring !== undefined) {
      const isRecurring = query.filter.isRecurring === 'true'
      queryBuilder.andWhere('booking.isRecurring = :isRecurring', { isRecurring })
    }
  }

  private applySorting (
    queryBuilder: SelectQueryBuilder<Booking>,
    query: ViewBookingIndexQuery
  ): void {
    if (query.sort && query.sort.length > 0) {
      query.sort.forEach((sortItem, index) => {
        const orderMethod = index === 0 ? 'orderBy' : 'addOrderBy'

        switch (sortItem.key) {
          case ViewBookingIndexSortQueryKey.STATUS:
            queryBuilder[orderMethod]('booking.status', sortItem.order === SortDirection.ASC ? 'ASC' : 'DESC')
            break
          case ViewBookingIndexSortQueryKey.START_DATE:
            queryBuilder[orderMethod]('booking.startDate', sortItem.order === SortDirection.ASC ? 'ASC' : 'DESC')
            break
          case ViewBookingIndexSortQueryKey.CREATED_AT:
            queryBuilder[orderMethod]('booking.createdAt', sortItem.order === SortDirection.ASC ? 'ASC' : 'DESC')
            break
        }
      })
    } else {
      queryBuilder.orderBy('booking.createdAt', 'DESC')
    }
  }
}

import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { Booking } from '../../entities/booking.entity.js'
import { BookingStatus } from '../../enums/booking-status.js'
import { ClientType } from '../../../client/client-type.js'
import { Client } from '../../../client/client.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { CareUser } from '../../../care-user/entities/care-user.entity.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { ContractType } from '../../../contract-type/entities/contract-type.entity.js'

class ContractTypeResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  constructor (contractType: ContractType) {
    this.uuid = contractType.uuid
    this.name = contractType.name
  }
}

class ContractResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: ContractTypeResponse })
  contractType: ContractTypeResponse

  constructor (contract: Contract) {
    assert(contract.contractType !== undefined, 'contractType is required')

    this.uuid = contract.uuid
    this.contractType = new ContractTypeResponse(contract.contractType)
  }
}

class CareUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  name: string | null

  constructor (careUser: CareUser) {
    this.uuid = careUser.uuid
    this.name = careUser.name
  }
}

class UserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  firstName: string | null

  @ApiProperty({ type: String, nullable: true })
  lastName: string | null

  @ApiProperty({ type: String, nullable: true })
  fullName: string | null

  constructor (user: User) {
    this.uuid = user.uuid
    this.firstName = user.firstName
    this.lastName = user.lastName
    this.fullName = user.fullName
  }
}

class ClientResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ enum: ClientType })
  type: ClientType

  @ApiProperty({ type: String })
  name: string

  constructor (client: Client) {
    this.uuid = client.uuid
    this.type = client.type
    this.name = client.name
  }
}

export class BookingResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ enum: BookingStatus })
  status: BookingStatus

  @ApiProperty({ type: Boolean })
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', nullable: true })
  startDate: string | null

  @ApiProperty({ type: String, format: 'date', nullable: true })
  endDate: string | null

  @ApiProperty({ type: Number, nullable: true })
  iterationIntervalWeeks: number | null

  @ApiProperty({ type: String, nullable: true })
  remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  remarksForPlanner: string | null

  @ApiProperty({ type: ClientResponse })
  client: ClientResponse

  @ApiProperty({ type: ContractResponse })
  contract: ContractResponse

  @ApiProperty({ type: CareUserResponse })
  careUser: CareUserResponse

  @ApiProperty({ type: UserResponse })
  createdByUser: UserResponse

  @ApiProperty({ type: UserResponse })
  updatedByUser: UserResponse

  constructor (booking: Booking) {
    assert(booking.client !== undefined, 'client is required')
    assert(booking.contract !== undefined, 'contract is required')
    assert(booking.careUser !== undefined, 'careUser is required')
    assert(booking.createdByUser !== undefined, 'createdByUser is required')
    assert(booking.updatedByUser !== undefined, 'updatedByUser is required')

    this.uuid = booking.uuid
    this.createdAt = booking.createdAt.toISOString()
    this.updatedAt = booking.updatedAt.toISOString()
    this.status = booking.status
    this.isRecurring = booking.isRecurring
    this.startDate = booking.startDate?.toString() ?? null
    this.endDate = booking.endDate?.toString() ?? null
    this.iterationIntervalWeeks = booking.iterationIntervalWeeks
    this.remarksForDriver = booking.remarksForDriver
    this.remarksForPlanner = booking.remarksForPlanner
    this.client = new ClientResponse(booking.client)
    this.contract = new ContractResponse(booking.contract)
    this.careUser = new CareUserResponse(booking.careUser)
    this.createdByUser = new UserResponse(booking.createdByUser)
    this.updatedByUser = new UserResponse(booking.updatedByUser)
  }
}

export class ViewBookingIndexResponse extends PaginatedOffsetResponse<BookingResponse> {
  @ApiProperty({ type: BookingResponse, isArray: true })
  declare items: BookingResponse[]

  constructor (items: Booking[], total: number, limit: number, offset: number) {
    const result = items.map(booking => new BookingResponse(booking))

    super(result, total, limit, offset)
  }
}

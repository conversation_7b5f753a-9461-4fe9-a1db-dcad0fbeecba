import { ApiProperty } from '@nestjs/swagger'
import { IsEnum } from 'class-validator'
import {
  SortDirection,
  SortDirectionApiProperty,
  SortQuery
} from '../../../../../utils/query/search.query.js'

export enum ViewBookingIndexSortQueryKey {
  STATUS = 'status',
  START_DATE = 'startDate',
  CREATED_AT = 'createdAt'
}

export class ViewBookingIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewBookingIndexSortQueryKey, enumName: 'ViewBookingIndexSortQueryKey' })
  @IsEnum(ViewBookingIndexSortQueryKey)
  key: ViewBookingIndexSortQueryKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}

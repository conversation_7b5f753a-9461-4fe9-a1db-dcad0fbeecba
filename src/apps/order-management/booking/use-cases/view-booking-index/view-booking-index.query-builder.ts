import { PaginatedOffsetQuery } from '@wisemen/pagination'
import { SortDirection } from '../../../../../utils/query/search.query.js'
import { BookingStatus } from '../../enums/booking-status.js'
import { ViewBookingIndexQuery } from './view-booking-index.query.js'
import { ViewBookingIndexSortQuery, ViewBookingIndexSortQueryKey } from './view-booking-index.sort-query.js'
import { ViewBookingIndexFilterQuery } from './view-booking-index-filter.query.js'

export class ViewBookingIndexQueryBuilder {
  private readonly query: ViewBookingIndexQuery

  constructor () {
    this.query = new ViewBookingIndexQuery()
    this.query.pagination = { limit: 10, offset: 0 }
  }

  withPagination (pagination: PaginatedOffsetQuery): this {
    this.query.pagination = pagination
    return this
  }

  withSort (key: ViewBookingIndexSortQueryKey, order: SortDirection): this {
    this.query.sort ??= []

    const sortQuery = new ViewBookingIndexSortQuery()
    sortQuery.key = key
    sortQuery.order = order

    this.query.sort.push(sortQuery)
    return this
  }

  withStatusFilter (status: BookingStatus): this {
    this.query.filter ??= new ViewBookingIndexFilterQuery()
    this.query.filter.status = status
    return this
  }

  withClientUuidFilter (clientUuid: string): this {
    this.query.filter ??= new ViewBookingIndexFilterQuery()
    this.query.filter.clientUuid = clientUuid
    return this
  }

  withContractUuidFilter (contractUuid: string): this {
    this.query.filter ??= new ViewBookingIndexFilterQuery()
    this.query.filter.contractUuid = contractUuid
    return this
  }

  withIsRecurringFilter (isRecurring: boolean): this {
    this.query.filter ??= new ViewBookingIndexFilterQuery()
    this.query.filter.isRecurring = isRecurring.toString()
    return this
  }

  build (): ViewBookingIndexQuery {
    return this.query
  }
}

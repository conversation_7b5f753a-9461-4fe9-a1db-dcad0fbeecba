import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { WiseDate, WiseDateColumn } from '@wisemen/wise-date'
import { ClientType, ClientTypeColumn } from '../../client/client-type.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { Contract } from '../../contract/entities/contract.entity.js'
import { CareUser } from '../../care-user/entities/care-user.entity.js'
import { Client } from '../../client/client.js'
import { BookingStatus } from '../enums/booking-status.js'
import { UserUuid } from '../../../../app/users/entities/user.uuid.js'
import { OrderRequest } from '../../order-request/entities/order-request.entity.js'
import { BookingUuid } from './booking.uuid.js'

@Entity()
export class Booking {
  @PrimaryGeneratedColumn('uuid')
  uuid: BookingUuid

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'uuid' })
  clientUuid: string

  @ClientTypeColumn()
  clientType: ClientType

  client?: Relation<Client>

  @Column({ type: 'enum', enum: BookingStatus })
  status: BookingStatus

  @Column({ type: 'boolean', default: false })
  isRecurring: boolean

  @WiseDateColumn({ nullable: true })
  startDate: WiseDate | null

  @WiseDateColumn({ nullable: true })
  endDate: WiseDate | null

  @Column({ type: 'int', nullable: true })
  iterationIntervalWeeks: number | null

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null

  @Column({ type: 'uuid' })
  contractUuid: string

  @ManyToOne(() => Contract)
  @JoinColumn({ name: 'contract_uuid' })
  contract?: Relation<Contract>

  @Column({ type: 'uuid' })
  careUserUuid: string

  @ManyToOne(() => CareUser)
  @JoinColumn({ name: 'care_user_uuid' })
  careUser?: Relation<CareUser>

  @Column({ type: 'uuid' })
  createdByUserUuid: UserUuid

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User>

  @Column({ type: 'uuid' })
  updatedByUserUuid: UserUuid

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by_user_uuid' })
  updatedByUser?: Relation<User>

  @Column({ type: 'uuid' })
  orderRequestUuid: string

  @OneToOne(() => OrderRequest)
  @JoinColumn({ name: 'order_request_uuid' })
  orderRequest?: Relation<OrderRequest>
}

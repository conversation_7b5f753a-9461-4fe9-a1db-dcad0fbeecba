import { ClientType } from '../client-type.js'
import { Client } from '../client.js'

export class TypesenseClient {
  id: string
  name: string
  crmUuid: string | null
  type: ClientType
  email: string | undefined
  phone: string | undefined
  mobilePhone: string | undefined
  country: string | undefined
  city: string | undefined
  postalCode: string | undefined
  streetName: string | undefined
  streetNumber: string | undefined
  unit: string | undefined
  coordinates: [number, number] | undefined

  constructor (client: Client) {
    this.id = client.uuid
    this.crmUuid = client.crmUuid
    this.name = client.name
    this.type = client.type
    this.email = client.email ?? undefined
    this.phone = client.phone ?? undefined
    this.mobilePhone = client.mobilePhone ?? undefined
    this.country = client.address?.country ?? undefined
    this.city = client.address?.city ?? undefined
    this.postalCode = client.address?.postalCode ?? undefined
    this.streetName = client.address?.streetName ?? undefined
    this.streetNumber = client.address?.streetNumber ?? undefined
    this.unit = client.address?.unit ?? undefined
    const coordinates = client.address?.coordinates
    this.coordinates = coordinates
      ? [coordinates.latitude, coordinates.longitude]
      : undefined
  }
}

import { TypesenseCollectionName } from '../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { RegisterTypesenseCollection } from '../../../../modules/typesense/collections/typesense-collection.decorator.js'
import { TypesenseCollection } from '../../../../modules/typesense/collections/typesense.collection.js'

@RegisterTypesenseCollection(TypesenseCollectionName.CLIENT)
export class ClientTypesenseCollection extends TypesenseCollection {
  readonly name = TypesenseCollectionName.CLIENT

  readonly searchableFields = [
    { name: 'crmUuid', type: 'string', sort: true, infix: true },
    { name: 'name', type: 'string', sort: true, infix: true },
    { name: 'email', type: 'string', sort: true, infix: true, optional: true },
    { name: 'phone', type: 'string', sort: true, infix: true, optional: true },
    { name: 'mobilePhone', type: 'string', sort: true, infix: true, optional: true },
    { name: 'country', type: 'string', sort: true, infix: true, optional: true },
    { name: 'city', type: 'string', sort: true, infix: true, optional: true },
    { name: 'postalCode', type: 'string', sort: true, infix: true, optional: true },
    { name: 'streetName', type: 'string', sort: true, infix: true, optional: true },
    { name: 'streetNumber', type: 'string', sort: true, infix: true, optional: true },
    { name: 'unit', type: 'string', sort: true, infix: true, optional: true }
  ] as const

  readonly filterableFields = [
    { name: 'type', type: 'string' },
    { name: 'coordinates', type: 'geopoint', optional: true }
  ] as const

  readonly referenceFields = [] as const
}

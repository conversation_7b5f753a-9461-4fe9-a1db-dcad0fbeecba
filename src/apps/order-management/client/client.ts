import { Address } from '../../../utils/address/address.js'
import { ClientType } from './client-type.js'
import { ClientId } from './client-id.js'

export class Client {
  public readonly uuid: string
  public readonly crmUuid: string
  public readonly name: string
  public readonly type: ClientType
  public readonly abbreviation: string | null
  public readonly address: Address | null
  public readonly email: string | null
  public readonly phone: string | null
  public readonly mobilePhone: string | null

  constructor ({ uuid, crmUuid, name, type, abbreviation, address, email, phone, mobilePhone }: {
    uuid: string
    crmUuid: string
    name: string
    type: ClientType
    abbreviation: string | null
    address: Address | null
    email: string | null
    phone: string | null
    mobilePhone: string | null
  }) {
    this.uuid = uuid
    this.crmUuid = crmUuid
    this.name = name
    this.type = type
    this.abbreviation = abbreviation
    this.address = address
    this.email = email
    this.phone = phone
    this.mobilePhone = mobilePhone
  }

  hasId (id: ClientId): boolean {
    return this.uuid === id.uuid && this.type === id.type
  }
}

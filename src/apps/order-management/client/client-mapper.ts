import { Injectable } from '@nestjs/common'
import { CareUser } from '../care-user/entities/care-user.entity.js'

import { Organization } from '../organization/entities/organization.entity.js'
import { Client } from './client.js'
import { ClientType } from './client-type.js'

@Injectable()
export class ClientMapper {
  mapCareUsers (careUsers: CareUser[]): Client[] {
    return careUsers.map(user => this.mapCareUser(user))
  }

  mapCareUser (careUser: CareUser): Client {
    return new Client({
      uuid: careUser.uuid,
      crmUuid: careUser.crmIndividualUuid,
      name: careUser.name,
      type: ClientType.CARE_USER,
      abbreviation: null,
      address: careUser.address,
      email: careUser.email,
      phone: careUser.phone,
      mobilePhone: careUser.mobilePhone
    })
  }

  mapOrganizations (organizations: Organization[]): Client[] {
    return organizations.map(organization => this.mapOrganization(organization))
  }

  mapOrganization (organization: Organization): Client {
    return new Client({
      uuid: organization.uuid,
      crmUuid: organization.crmBusinessUuid,
      name: organization.name,
      type: ClientType.ORGANIZATION,
      abbreviation: organization.abbreviation,
      address: organization.address,
      email: organization.email,
      phone: organization.phone,
      mobilePhone: organization.mobilePhone
    })
  }
}

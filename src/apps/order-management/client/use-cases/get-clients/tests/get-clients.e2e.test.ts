import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { stringify } from 'qs'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TypesenseCollectionService } from '../../../../../../modules/typesense/services/typesense-collection.service.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { Client } from '../../../client.js'
import { ClientType } from '../../../client-type.js'
import { GetClientsQueryBuilder } from '../query/get-clients.query.builder.js'
import { TypesenseCollectionName } from '../../../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'
import { AddressResponse } from '../../../../../../utils/address/address-response.js'

describe('Get clients end to end tests', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser
  let typesense: TypesenseCollectionService

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()
    typesense = setup.testModule.get(TypesenseCollectionService, { strict: false })
  })

  after(async () => await setup.teardown())

  it('Gets the clients successfully', async () => {
    const client = new Client({
      uuid: randomUUID(),
      crmUuid: randomUUID(),
      name: 'John Doe',
      type: ClientType.CARE_USER,
      abbreviation: null,
      address: new AddressBuilder()
        .withStreetName('Main Street')
        .withStreetNumber('123')
        .withCity('Anytown')
        .withPostalCode('12345')
        .withCountry('Countryland')
        .build(),
      email: '<EMAIL>',
      phone: '1234567890',
      mobilePhone: '0987654321'
    })

    await typesense.importManually(TypesenseCollectionName.CLIENT, [client])

    const query = new GetClientsQueryBuilder()
      .withSearch(client.name)
      .withLimit(10)
      .withOffset(0)
      .build()

    const response = await request(setup.httpServer)
      .get('/clients')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body).toStrictEqual(expect.objectContaining({
      items: [expect.objectContaining({
        uuid: client.uuid,
        name: client.name,
        type: client.type,
        email: client.email,
        phone: client.phone,
        mobilePhone: client.mobilePhone,
        address: new AddressResponse(new AddressBuilder()
          .withStreetName('Main Street')
          .withStreetNumber('123')
          .withCity('Anytown')
          .withPostalCode('12345')
          .withCountry('Countryland')
          .build()
        )
      })],
      meta: {
        total: 1,
        offset: 0,
        limit: 10
      }
    }))
  })
})

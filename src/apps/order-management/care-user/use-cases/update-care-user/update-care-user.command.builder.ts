import { randomUUID } from 'crypto'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { UpdateCareUserCommand } from './update-care-user.command.js'

export class UpdateCareUserCommandBuilder {
  private command: UpdateCareUserCommand

  constructor () {
    this.command = new UpdateCareUserCommand()
    this.command.name = 'Care User'
    this.command.externalId = randomUUID()
    this.command.crmIndividualUuid = randomUUID()
    this.command.email = null
    this.command.phone = null
    this.command.mobilePhone = null
    this.command.socialSecurityNumber = null
    this.command.address = new AddressCommandBuilder().build()
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withExternalId (externalId: string | null): this {
    this.command.externalId = externalId
    return this
  }

  withCrmIndividualUuid (crmIndividualUuid: string): this {
    this.command.crmIndividualUuid = crmIndividualUuid
    return this
  }

  withEmail (email: string | null): this {
    this.command.email = email
    return this
  }

  withPhone (phone: string | null): this {
    this.command.phone = phone
    return this
  }

  withMobilePhone (mobilePhone: string | null): this {
    this.command.mobilePhone = mobilePhone
    return this
  }

  withSocialSecurityNumber (socialSecurityNumber: string | null): this {
    this.command.socialSecurityNumber = socialSecurityNumber
    return this
  }

  withAddress (address: AddressCommand | null): this {
    this.command.address = address
    return this
  }

  build (): UpdateCareUserCommand {
    return this.command
  }
}

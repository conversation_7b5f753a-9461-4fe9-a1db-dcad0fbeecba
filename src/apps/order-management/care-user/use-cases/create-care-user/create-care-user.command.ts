import { ApiProperty } from '@nestjs/swagger'
import { IsNullable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { IsNotEmpty, IsObject, IsString, IsUUID, ValidateNested } from 'class-validator'
import { AddressCommand } from '../../../../../utils/address/address-command.js'

export class CreateCareUserCommand {
  @ApiProperty({
    description: 'The id of the care user',
    type: 'string',
    nullable: true
  })
  @IsString()
  @IsNullable()
  externalId: string

  @ApiProperty({
    description: 'The name of the care user',
    type: 'string'
  })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  crmIndividualUuid: string

  @ApiProperty({ type: String, format: 'email', nullable: true })
  @IsString()
  @IsNullable()
  email: string | null

  @ApiProperty({ type: String, format: 'phone', nullable: true })
  @IsString()
  @IsNullable()
  phone: string | null

  @ApiProperty({ type: String, format: 'phone', nullable: true })
  @IsString()
  @IsNullable()
  mobilePhone: string | null

  @ApiProperty({ type: String, format: 'ssn', nullable: true })
  @IsString()
  @IsNullable()
  socialSecurityNumber: string | null

  @ApiProperty({ type: AddressCommand })
  @IsObject()
  @Type(() => AddressCommand)
  @ValidateNested()
  @IsNullable()
  address: AddressCommand | null
}

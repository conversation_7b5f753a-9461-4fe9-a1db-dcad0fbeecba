import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { CareUser } from '../../entities/care-user.entity.js'
import { CrmClient } from '../../../../../modules/crm/crm.client.js'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { UpdateOrCreateCareUserFromCrmCommandBuilder } from '../update-or-create-from-crm/update-or-create-from-crm.command.builder.js'
import { UpdateOrCreateCareUserFromCrmUseCase } from '../update-or-create-from-crm/update-or-create-from-crm.use-case.js'
import { GetCareUserBasedOnCrmUuidResponse } from './get-care-user-based-on-crm-uuid.response.js'

@Injectable()
export class GetCareUserBasedOnCrmUuidUseCase {
  constructor (
    @InjectRepository(CareUser)
    private readonly careUserRepo: Repository<CareUser>,
    private readonly crmClient: CrmClient,
    private readonly updateOrCreateCareUserFromCrmUseCase: UpdateOrCreateCareUserFromCrmUseCase
  ) {}

  async execute (crmIndividualUuid: string): Promise<GetCareUserBasedOnCrmUuidResponse> {
    const careUser = await this.careUserRepo.findOneBy({ crmIndividualUuid })

    if (careUser) {
      return new GetCareUserBasedOnCrmUuidResponse(careUser)
    }

    const individual = await this.crmClient.getIndividualOrFail(crmIndividualUuid)

    const command = new UpdateOrCreateCareUserFromCrmCommandBuilder()
      .withName(individual.firstName + ' ' + individual.lastName)
      .withCrmIndividualUuid(individual.uuid)
      .withEmail(individual.email)
      .withPhone(individual.phone)
      .withMobilePhone(individual.mobilePhone)
      .withSocialSecurityNumber(individual.socialSecurityNumber)
      .withAddress(new AddressCommandBuilder()
        .withStreetName(individual.location?.address.streetName ?? null)
        .withStreetNumber(individual.location?.address.streetNumber ?? null)
        .withUnit(individual.location?.address.unit ?? null)
        .withPostalCode(individual.location?.address.postalCode ?? null)
        .withCity(individual.location?.address.city ?? null)
        .withCountry(individual.location?.address.country ?? null)
        .withCoordinates(new CoordinatesCommandBuilder()
          .withLatitude(individual.location?.address.coordinates?.latitude ?? 0)
          .withLongitude(individual.location?.address.coordinates?.longitude ?? 0)
          .build())
        .build())
      .build()

    const careUserUuid = await this.updateOrCreateCareUserFromCrmUseCase.execute(command)

    const careUserCreated = await this.careUserRepo.findOneByOrFail({ uuid: careUserUuid })
    return new GetCareUserBasedOnCrmUuidResponse(careUserCreated)
  }
}

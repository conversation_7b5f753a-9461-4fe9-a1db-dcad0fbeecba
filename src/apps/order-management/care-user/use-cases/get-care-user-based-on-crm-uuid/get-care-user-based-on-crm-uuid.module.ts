import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { CareUser } from '../../entities/care-user.entity.js'
import { CrmModule } from '../../../../../modules/crm/crm.module.js'
import { UpdateOrCreateCareUserFromCrmModule } from '../update-or-create-from-crm/update-or-create-from-crm.module.js'
import { GetCareUserBasedOnCrmUuidUseCase } from './get-care-user-based-on-crm-uuid.use-case.js'
import { GetCareUserBasedOnCrmUuidController } from './get-care-user-based-on-crm-uuid.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([CareUser]),
    CrmModule,
    UpdateOrCreateCareUserFromCrmModule
  ],
  providers: [GetCareUserBasedOnCrmUuidUseCase],
  controllers: [GetCareUserBasedOnCrmUuidController]
})
export class GetCareUserBasedOnCrmUuidModule {}

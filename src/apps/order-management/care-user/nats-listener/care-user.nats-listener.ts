import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { IntegrationEvent } from '../../../../modules/integration-events/integration-event.js'
import { NatsSubscriber } from '../../../../modules/nats/nats-application/subscribers/nats-subscriber.decorator.js'
import { NatsMessageData } from '../../../../modules/nats/nats-application/parameters/nats-message-data.decorator.js'
import { NatsMsgDataJsonPipe } from '../../../../modules/nats/nats-application/parameters/pipes/nats-message-data-json.pipe.js'
import { IntegrationEventType } from '../../../../modules/integration-events/integration-event.type.js'
import { WiseCrmNatsClient } from '../../../../modules/nats/nats-application/clients/nats-crm.client.js'
import { natsSubject } from '../../../../modules/nats/nats-application/nats-subject.js'
import { Address } from '../../../../utils/address/address.js'
import { OnNatsMessage } from '../../../../modules/nats/nats-application/message-handler/on-nats-message.decorator.js'
import { AddressCommandBuilder } from '../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../utils/address/address-command.js'
import { UpdateOrCreateCareUserFromCrmUseCase } from '../use-cases/update-or-create-from-crm/update-or-create-from-crm.use-case.js'
import { UpdateOrCreateCareUserFromCrmCommandBuilder } from '../use-cases/update-or-create-from-crm/update-or-create-from-crm.command.builder.js'
import { CareUserCreatedIntegrationEventData } from './care-user-created-data.js'
import { CareUserUpdatedIntegrationEventData } from './care-user-updated-data.js'

const CARE_USER_REQUEST_SUBJECT = '{env}.wise-crm.tenant.{tenantUuid}.individual.>'

@NatsSubscriber(configService => ({
  client: WiseCrmNatsClient,
  subject: natsSubject(CARE_USER_REQUEST_SUBJECT, {
    env: configService.getOrThrow<string>('NODE_ENV'),
    tenantUuid: configService.getOrThrow<string>('CRM_TENANT_UUID')
  })
}))
export class CareUserNatsSubscriber {
  constructor (
    private readonly updateOrCreateCareUserUseCase: UpdateOrCreateCareUserFromCrmUseCase
  ) {}

  @OnNatsMessage()
  handleEvent (
    @NatsMessageData(NatsMsgDataJsonPipe) event: IntegrationEvent
  ): Promise<void> | void {
    switch (event.type) {
      case IntegrationEventType.INDIVIDUAL_CREATED: return this.handleIndividualCreatedEvent(event)
      case IntegrationEventType.INDIVIDUAL_UPDATED: return this.handleIndividualUpdatedEvent(event)
      default: throw new Error(`Unsupported type: ${event.type}`)
    }
  }

  async handleIndividualCreatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as CareUserCreatedIntegrationEventData

    const createCareUserCommand = new UpdateOrCreateCareUserFromCrmCommandBuilder()
      .withName(data.firstName + ' ' + data.lastName)
      .withCrmIndividualUuid(data.individualUuid)
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .withSocialSecurityNumber(data.socialSecurityNumber)
      .withAddress(this.buildAddressCommand(data.address))
      .build()

    await this.updateOrCreateCareUserUseCase.execute(createCareUserCommand)
  }

  async handleIndividualUpdatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as CareUserUpdatedIntegrationEventData

    const updateCareUserCommand = new UpdateOrCreateCareUserFromCrmCommandBuilder()
      .withName(data.firstName + ' ' + data.lastName)
      .withCrmIndividualUuid(data.individualUuid)
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .withSocialSecurityNumber(data.socialSecurityNumber)
      .withAddress(this.buildAddressCommand(data.address))
      .build()

    await this.updateOrCreateCareUserUseCase.execute(updateCareUserCommand)
  }

  private buildAddressCommand (addressString: string | null): AddressCommand | null {
    let addressCommand: AddressCommand | null = null

    if (addressString !== null) {
      const address = JSON.parse(addressString) as Address
      addressCommand = new AddressCommandBuilder()
        .withStreetName(address.streetName ?? null)
        .withStreetNumber(address.streetNumber ?? null)
        .withUnit(address.unit ?? null)
        .withCity(address.city ?? null)
        .withPostalCode(address.postalCode ?? null)
        .withCountry(address.country ?? null)
        .withCoordinates(new CoordinatesCommandBuilder()
          .withLatitude(address.coordinates?.latitude ?? 0)
          .withLongitude(address.coordinates?.longitude ?? 0)
          .build())
        .build()
    }

    return addressCommand
  }
}

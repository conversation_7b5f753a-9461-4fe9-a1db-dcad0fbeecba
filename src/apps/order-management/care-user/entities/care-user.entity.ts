import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'
import { AddressColumn } from '../../../../utils/address/address-column.js'
import { Address } from '../../../../utils/address/address.js'
import { CareUserConditions, CareUserConditionsColumn } from '../enums/care-user-conditions.enum.js'

@Entity()
export class CareUser {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'varchar', unique: true, nullable: true })
  externalId: string | null

  @Index()
  @Column({ type: 'uuid', unique: true })
  crmIndividualUuid: string

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'varchar', nullable: true })
  email: string | null

  @Column({ type: 'varchar', nullable: true })
  phone: string | null

  @Column({ type: 'varchar', nullable: true })
  mobilePhone: string | null

  @Column({ type: 'varchar', nullable: true })
  socialSecurityNumber: string | null

  @AddressColumn({ nullable: true })
  address: Address | null

  @Column({ type: 'boolean', default: false })
  mayNotBeCombined: boolean

  @CareUserConditionsColumn({ array: true, default: [] })
  conditions: CareUserConditions[]

  @Column({ type: 'int', default: 0 })
  discountPerKm: number

  @Column({ type: 'int', default: 0 })
  discountPerMin: number

  @Column({ type: 'boolean', default: false })
  isPabBudgetHolder: boolean

  @Column({ type: 'boolean', default: false })
  hasServiceChecks: boolean

  @Column({ type: 'varchar', nullable: true })
  serviceChecksAccountNumber: string | null

  @Column({ type: 'boolean', default: false })
  hasAssistanceDog: boolean

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null
}

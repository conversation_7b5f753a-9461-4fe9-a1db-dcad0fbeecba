import { randomUUID } from 'crypto'
import { CareUser } from '../entities/care-user.entity.js'
import { CareUserConditions } from '../enums/care-user-conditions.enum.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'
import { Address } from '../../../../utils/address/address.js'
import { generateUuid } from '../../../../modules/database-seeder/uuid-generator.js'

export class CareUserEntityBuilder {
  private readonly careUser: CareUser = new CareUser()

  constructor () {
    this.careUser.uuid = randomUUID()
    this.careUser.externalId = randomUUID()
    this.careUser.name = '<PERSON>'
    this.careUser.crmIndividualUuid = randomUUID()
    this.careUser.address = new AddressBuilder().build()
    // Note: New fields are not set by default to avoid database issues
    // until the migration is applied
  }

  withUuid (uuid: string): this {
    this.careUser.uuid = uuid
    return this
  }

  withExternalId (externalId: string | null): this {
    this.careUser.externalId = externalId
    return this
  }

  withCrmIndividualUuid (crmIndividualUuid: string): this {
    this.careUser.crmIndividualUuid = crmIndividualUuid
    this.careUser.uuid = generateUuid(crmIndividualUuid)
    return this
  }

  withName (name: string): this {
    this.careUser.name = name
    return this
  }

  withEmail (email: string | null): this {
    this.careUser.email = email
    return this
  }

  withPhone (phone: string | null): this {
    this.careUser.phone = phone
    return this
  }

  withMobilePhone (mobilePhone: string | null): this {
    this.careUser.mobilePhone = mobilePhone
    return this
  }

  withSocialSecurityNumber (socialSecurityNumber: string | null): this {
    this.careUser.socialSecurityNumber = socialSecurityNumber
    return this
  }

  withAddress (address: Address | null): this {
    this.careUser.address = address
    return this
  }

  withMayNotBeCombined (mayNotBeCombined: boolean): this {
    this.careUser.mayNotBeCombined = mayNotBeCombined
    return this
  }

  withConditions (conditions: CareUserConditions[]): this {
    this.careUser.conditions = conditions
    return this
  }

  withIsPabBudgetHolder (isPabBudgetHolder: boolean): this {
    this.careUser.isPabBudgetHolder = isPabBudgetHolder
    return this
  }

  withHasServiceChecks (hasServiceChecks: boolean): this {
    this.careUser.hasServiceChecks = hasServiceChecks
    return this
  }

  withServiceChecksAccountNumber (serviceChecksAccountNumber: string | null): this {
    this.careUser.serviceChecksAccountNumber = serviceChecksAccountNumber
    return this
  }

  withHasAssistanceDog (hasAssistanceDog: boolean): this {
    this.careUser.hasAssistanceDog = hasAssistanceDog
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.careUser.remarksForPlanner = remarksForPlanner
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.careUser.remarksForDriver = remarksForDriver
    return this
  }

  withDiscountPerKm (discountPerKm: number): this {
    this.careUser.discountPerKm = discountPerKm
    return this
  }

  withDiscountPerMin (discountPerMin: number): this {
    this.careUser.discountPerMin = discountPerMin
    return this
  }

  build (): CareUser {
    return this.careUser
  }
}

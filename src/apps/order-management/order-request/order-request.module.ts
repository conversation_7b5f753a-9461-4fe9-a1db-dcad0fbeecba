import { Modu<PERSON> } from '@nestjs/common'
import { CreateOrderRequestModule } from './use-cases/create-order-request/create-order-request.module.js'
import { UpdateOrderRequestModule } from './use-cases/update-order-request/update-order-request.module.js'
import { ViewOrderRequestIndexModule } from './use-cases/view-order-request-index/view-order-request-index.module.js'
import { ViewOrderRequestDetailModule } from './use-cases/view-order-request-detail/view-order-request-detail.module.js'
import { AcceptOrderRequestModule } from './use-cases/accept-order-request/accept-order-request.module.js'

@Module({
  imports: [
    CreateOrderRequestModule,
    UpdateOrderRequestModule,
    ViewOrderRequestIndexModule,
    ViewOrderRequestDetailModule,
    AcceptOrderRequestModule
  ]
})
export class OrderRequestModule {}

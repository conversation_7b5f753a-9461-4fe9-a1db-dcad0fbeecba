import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { WiseDate } from '@wisemen/wise-date'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { ClientType, ClientTypeApiProperty } from '../../../client/client-type.js'
import { UserUuid } from '../../../../../app/users/entities/user.uuid.js'

@OneOfMeta(DomainEventLog, DomainEventType.ORDER_REQUEST_ACCEPTED)
export class OrderRequestAcceptedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly orderRequestUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly clientUuid: string

  @ClientTypeApiProperty()
  readonly clientType: ClientType

  @ApiProperty()
  readonly isRecurring: boolean

  @ApiProperty({ type: String, nullable: true })
  readonly startDate: WiseDate | null

  @ApiProperty({ type: Number, nullable: true })
  readonly iterationIntervalWeeks: number | null

  @ApiProperty({ type: String, nullable: true })
  readonly remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  readonly remarksForPlanner: string | null

  @ApiProperty({ format: 'uuid' })
  readonly contractUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly careUserUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly createdByUserUuid: UserUuid

  @ApiProperty({ format: 'uuid' })
  readonly updatedByUserUuid: UserUuid

  @ApiProperty({ format: 'uuid' })
  readonly acceptedByUserUuid: UserUuid

  constructor (orderRequest: OrderRequest, acceptedByUserUuid: UserUuid) {
    this.orderRequestUuid = orderRequest.uuid
    this.clientUuid = orderRequest.clientUuid
    this.clientType = orderRequest.clientType
    this.isRecurring = orderRequest.isRecurring
    this.startDate = orderRequest.startDate
    this.iterationIntervalWeeks = orderRequest.iterationIntervalWeeks
    this.remarksForDriver = orderRequest.remarksForDriver
    this.remarksForPlanner = orderRequest.remarksForPlanner
    this.contractUuid = orderRequest.contractUuid
    this.careUserUuid = orderRequest.careUserUuid
    this.createdByUserUuid = orderRequest.createdByUserUuid
    this.updatedByUserUuid = orderRequest.updatedByUserUuid
    this.acceptedByUserUuid = acceptedByUserUuid
  }
}

@RegisterDomainEvent(DomainEventType.ORDER_REQUEST_ACCEPTED, 1)
export class OrderRequestAcceptedEvent extends DomainEvent<OrderRequestAcceptedEventContent> {
  constructor (orderRequest: OrderRequest, acceptedByUserUuid: UserUuid) {
    super({ content: new OrderRequestAcceptedEventContent(orderRequest, acceptedByUserUuid) })
  }
}

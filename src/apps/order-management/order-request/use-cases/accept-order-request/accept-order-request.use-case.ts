import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../../modules/auth/auth.context.js'
import { validateRequestedTransportOrder } from '../../../requested-transport-order/validators/requested-transport-order.validator.js'
import { OrderRequestNotFoundError } from '../../errors/order-request-not-found.error.js'
import { OrderRequestValidationError } from '../../errors/order-request-validation.error.js'
import { OrderRequestNoTransportOrdersError } from '../../errors/order-request-no-transport-orders.error.js'
import { AcceptOrderRequestRepository } from './accept-order-request.repository.js'
import { OrderRequestAcceptedEvent } from './order-request-accepted.event.js'

@Injectable()
export class AcceptOrderRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly repository: AcceptOrderRequestRepository,
    private readonly authContext: AuthContext,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    orderRequestUuid: string
  ): Promise<void> {
    const orderRequest = await this.repository.findOrderRequestWithDetails(orderRequestUuid)
    if (!orderRequest) {
      throw new OrderRequestNotFoundError(orderRequestUuid)
    }

    // Get requested transport orders
    const requestedTransportOrders
    = await this.repository.findRequestedTransportOrders(orderRequestUuid)

    // Validate that there are transport orders
    if (requestedTransportOrders.length === 0) {
      throw new OrderRequestNoTransportOrdersError(orderRequestUuid)
    }

    // Validate all requested transport orders
    const validationErrors: Array<{
      requestedTransportOrderUuid: string
      missingFields: string[]
      nonAllowedFields: string[]
    }> = []

    for (const rto of requestedTransportOrders) {
      assert(orderRequest.contract?.contractType, 'Contract type must be defined for validation')
      const validation = validateRequestedTransportOrder(
        rto,
        orderRequest.isRecurring,
        orderRequest.contract?.contractType?.name
      )

      if (validation.missingFields.length > 0 || validation.nonAllowedFields.length > 0) {
        validationErrors.push({
          requestedTransportOrderUuid: rto.uuid,
          missingFields: validation.missingFields,
          nonAllowedFields: validation.nonAllowedFields
        })
      }
    }

    // Throw validation error if any transport orders are invalid
    if (validationErrors.length > 0) {
      throw new OrderRequestValidationError(validationErrors)
    }

    // Accept the order request
    const acceptedByUserUuid = this.authContext.getUserUuidOrFail()

    await transaction(this.dataSource, async () => {
      await this.repository.acceptOrderRequest(
        orderRequestUuid,
        acceptedByUserUuid
      )

      await this.eventEmitter.emitOne(
        new OrderRequestAcceptedEvent(orderRequest, acceptedByUserUuid)
      )
    })
  }
}

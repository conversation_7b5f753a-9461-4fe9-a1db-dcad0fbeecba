import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { AuthModule } from '../../../../../modules/auth/auth.module.js'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { RequestedTransportOrder } from '../../../requested-transport-order/requested-transport-order.entity.js'
import { Contract } from '../../../contract/entities/contract.entity.js'
import { AcceptOrderRequestUseCase } from './accept-order-request.use-case.js'
import { AcceptOrderRequestRepository } from './accept-order-request.repository.js'
import { AcceptOrderRequestController } from './accept-order-request.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OrderRequest,
      RequestedTransportOrder,
      Contract
    ]),
    DomainEventEmitterModule,
    AuthModule
  ],
  providers: [
    AcceptOrderRequestUseCase,
    AcceptOrderRequestRepository
  ],
  controllers: [
    AcceptOrderRequestController
  ],
  exports: [
    AcceptOrderRequestUseCase
  ]
})
export class AcceptOrderRequestModule {}

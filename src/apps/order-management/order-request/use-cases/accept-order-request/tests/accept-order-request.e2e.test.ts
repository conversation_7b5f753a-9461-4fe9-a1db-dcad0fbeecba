import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { DataSource } from 'typeorm'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { OrderRequest } from '../../../entities/order-request.entity.js'
import { OrderRequestStatus } from '../../../enums/order-request-status.js'
import { OrderRequestNotFoundError } from '../../../errors/order-request-not-found.error.js'
import { OrderRequestNoTransportOrdersError } from '../../../errors/order-request-no-transport-orders.error.js'
import { setupAcceptOrderRequestTestContext, AcceptOrderRequestTestContext } from './accept-order-request-test-context.js'

describe('Accept Order Request E2E', () => {
  let setup: EndToEndTestSetup
  let dataSource: DataSource
  let adminUser: TestUser
  let testContext: AcceptOrderRequestTestContext

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    dataSource = setup.dataSource
    adminUser = await setup.authContext.getAdminUser()

    // Setup test data
    testContext = await setupAcceptOrderRequestTestContext(dataSource.manager)
  })

  after(async () => {
    await setup.teardown()
  })

  it('successfully accepts an order request', async () => {
    const response = await request(setup.httpServer)
      .post(`/order-requests/${testContext.orderRequest.uuid}/accept`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(204)

    // Verify order request was updated
    const updatedOrderRequest = await dataSource.manager.findOneOrFail(OrderRequest, {
      where: { uuid: testContext.orderRequest.uuid }
    })
    expect(updatedOrderRequest.status).toBe(OrderRequestStatus.ACCEPTED)
    expect(updatedOrderRequest.acceptedByUserUuid).toBe(adminUser.user.uuid)
  })

  it('returns 404 when order request does not exist', async () => {
    const nonExistentUuid = '123e4567-e89b-12d3-a456-************'

    const response = await request(setup.httpServer)
      .post(`/order-requests/${nonExistentUuid}/accept`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(404)
    expect(response).toHaveApiError(new OrderRequestNotFoundError(nonExistentUuid))
  })

  it('returns 400 when order request has no transport orders', async () => {
    // Create order request without transport orders
    const emptyContext = await setupAcceptOrderRequestTestContext(dataSource.manager, {
      includeTransportOrder: false
    })

    const response = await request(setup.httpServer)
      .post(`/order-requests/${emptyContext.orderRequest.uuid}/accept`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(400)
    expect(response)
      .toHaveApiError(new OrderRequestNoTransportOrdersError(emptyContext.orderRequest.uuid))
  })

  it('returns 400 when transport orders are invalid', async () => {
    // Create order request with invalid transport order (missing required date)
    const invalidContext = await setupAcceptOrderRequestTestContext(dataSource.manager, {
      transportOrderDate: null // Missing required date for non-recurring private contract
    })

    const response = await request(setup.httpServer)
      .post(`/order-requests/${invalidContext.orderRequest.uuid}/accept`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(400)
    expect(response.body.errors[0].code).toBe('order_request_validation_failed')
    expect(response.body.errors[0].detail).toContain('Order request validation failed for 1 requested transport order(s)')
  })
})

import { randomUUID } from 'crypto'
import { EntityManager } from 'typeorm'
import { Time } from '@wisemen/time'
import { OrderRequest } from '../../../entities/order-request.entity.js'
import { OrderRequestEntityBuilder } from '../../../entities/order-request.entity.builder.js'
import { RequestedTransportOrder } from '../../../../requested-transport-order/requested-transport-order.entity.js'
import { RequestedTransportOrderEntityBuilder } from '../../../../requested-transport-order/requested-transport-order.entity.builder.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { OrganizationEntityBuilder } from '../../../../organization/builders/organization.entity.builder.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { OrderRequestStatus } from '../../../enums/order-request-status.js'
import { ContractTypeName } from '../../../../contract-type/enums/contract-type-name.enum.js'
import { ClientType } from '../../../../client/client-type.js'
import { ClientId } from '../../../../client/client-id.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { Organization } from '../../../../organization/entities/organization.entity.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'

export interface AcceptOrderRequestTestContext {
  orderRequest: OrderRequest
  requestedTransportOrder: RequestedTransportOrder
  contract: Contract
  contractType: ContractType
  organization: Organization
  careUser: CareUser
  createdByUser: User
}

export async function setupAcceptOrderRequestTestContext (
  entityManager: EntityManager,
  options: {
    orderRequestStatus?: OrderRequestStatus
    contractTypeName?: ContractTypeName
    isRecurring?: boolean
    includeTransportOrder?: boolean
    transportOrderDate?: string | null
  } = {}
): Promise<AcceptOrderRequestTestContext> {
  const {
    orderRequestStatus = OrderRequestStatus.REQUESTED,
    contractTypeName = ContractTypeName.PRIVATE,
    isRecurring = false,
    includeTransportOrder = true,
    transportOrderDate = '2024-12-01'
  } = options

  const user = new UserEntityBuilder()
    .withEmail(randomUUID() + '@mail.com')
    .build()

  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder().build()
  const pricingFormula = new PricingFormulaEntityBuilder().build()
  const contractType = new ContractTypeEntityBuilder()
    .withName(contractTypeName)
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const organization = new OrganizationEntityBuilder().build()
  const careUser = new CareUserEntityBuilder().build()

  const contract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(organization.uuid, ClientType.ORGANIZATION))
    .build()

  const orderRequest = new OrderRequestEntityBuilder()
    .withStatus(orderRequestStatus)
    .withContractUuid(contract.uuid)
    .withCareUserUuid(careUser.uuid)
    .withCreatedByUserUuid(user.uuid)
    .withUpdatedByUserUuid(user.uuid)
    .withClientUuid(organization.uuid)
    .withClientType(ClientType.ORGANIZATION)
    .withIsRecurring(isRecurring)
    .build()

  let requestedTransportOrder: RequestedTransportOrder | null = null
  if (includeTransportOrder) {
    requestedTransportOrder = new RequestedTransportOrderEntityBuilder()
      .withOrderRequestUuid(orderRequest.uuid)
      .withDate(transportOrderDate)
      .withTargetTime(new Time('10:00'))
      .withTargetTimeFlexibilityBeforeInMinutes(15)
      .withTargetTimeFlexibilityAfterInMinutes(15)
      .withPickupAddress(new AddressBuilder()
        .withPlaceName('Pickup Location')
        .withCity('Brussels')
        .withPostalCode('1000')
        .withStreetName('Main Street')
        .withStreetNumber('123')
        .build())
      .withPickupStopDuration(5)
      .withPickupActionDuration(10)
      .withPickupRemarksForDriver('Pickup remarks')
      .withPickupRemarksForPlanner('Pickup planner remarks')
      .withDropOffAddress(new AddressBuilder()
        .withPlaceName('Drop-off Location')
        .withCity('Antwerp')
        .withPostalCode('2000')
        .withStreetName('Second Street')
        .withStreetNumber('456')
        .build())
      .withDropOffStopDuration(5)
      .withDropOffActionDuration(10)
      .withDropOffRemarksForDriver('Drop off remarks')
      .withDropOffRemarksForPlanner('Drop off planner remarks')
      .build()
  }

  await entityManager.insert(User, user)
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)
  await entityManager.insert(PricingFormula, pricingFormula)
  await entityManager.insert(ContractType, contractType)
  await entityManager.insert(Organization, organization)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, contract)
  await entityManager.insert(OrderRequest, orderRequest)

  if (requestedTransportOrder) {
    await entityManager.insert(RequestedTransportOrder, requestedTransportOrder)
  }

  return {
    orderRequest,
    requestedTransportOrder: requestedTransportOrder!,
    contract,
    contractType,
    organization,
    careUser,
    createdByUser: user
  }
}

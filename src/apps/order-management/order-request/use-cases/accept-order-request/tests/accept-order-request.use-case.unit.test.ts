import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Time } from '@wisemen/time'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../../../modules/auth/auth.context.js'
import { OrderRequestEntityBuilder } from '../../../entities/order-request.entity.builder.js'
import { RequestedTransportOrderEntityBuilder } from '../../../../requested-transport-order/requested-transport-order.entity.builder.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { OrderRequestStatus } from '../../../enums/order-request-status.js'
import { ContractTypeName } from '../../../../contract-type/enums/contract-type-name.enum.js'
import { OrderRequestNotFoundError } from '../../../errors/order-request-not-found.error.js'
import { OrderRequestValidationError } from '../../../errors/order-request-validation.error.js'
import { OrderRequestNoTransportOrdersError } from '../../../errors/order-request-no-transport-orders.error.js'
import { AcceptOrderRequestUseCase } from '../accept-order-request.use-case.js'
import { AcceptOrderRequestRepository } from '../accept-order-request.repository.js'
import { OrderRequestAcceptedEvent } from '../order-request-accepted.event.js'
import { generateUserUuid } from '../../../../../../app/users/entities/user.uuid.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'

describe('AcceptOrderRequestUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('throws a not found error when the order request does not exist', async () => {
    const repository = createStubInstance(AcceptOrderRequestRepository)
    repository.findOrderRequestWithDetails.resolves(null)

    const useCase = new AcceptOrderRequestUseCase(
      stubDataSource(),
      repository,
      createStubInstance(AuthContext),
      createStubInstance(DomainEventEmitter)
    )

    const orderRequestUuid = randomUUID()

    await expect(useCase.execute(orderRequestUuid))
      .rejects.toThrow(OrderRequestNotFoundError)
  })

  it('throws an error when order request has no transport orders', async () => {
    const repository = createStubInstance(AcceptOrderRequestRepository)
    const orderRequest = new OrderRequestEntityBuilder()
      .withStatus(OrderRequestStatus.REQUESTED)
      .build()
    repository.findOrderRequestWithDetails.resolves(orderRequest)
    repository.findRequestedTransportOrders.resolves([])

    const useCase = new AcceptOrderRequestUseCase(
      stubDataSource(),
      repository,
      createStubInstance(AuthContext),
      createStubInstance(DomainEventEmitter)
    )

    const orderRequestUuid = randomUUID()

    await expect(useCase.execute(orderRequestUuid))
      .rejects.toThrow(OrderRequestNoTransportOrdersError)
  })

  it('throws validation error when transport orders are invalid', async () => {
    const repository = createStubInstance(AcceptOrderRequestRepository)
    const contractType = new ContractTypeEntityBuilder()
      .withName(ContractTypeName.PRIVATE)
      .build()
    const contract = new ContractEntityBuilder().build()
    contract.contractType = contractType

    const orderRequest = new OrderRequestEntityBuilder()
      .withStatus(OrderRequestStatus.REQUESTED)
      .withIsRecurring(false)
      .build()
    orderRequest.contract = contract

    // Create invalid transport order (missing required date for non-recurring private contract)
    const invalidTransportOrder = new RequestedTransportOrderEntityBuilder()
      .withDate(null) // Missing required date
      .withDaysOfWeek([]) // Should be null for non-recurring
      .withPlanOnHolidays(null) // Should be null for non-recurring
      .build()
    invalidTransportOrder.orderRequest = orderRequest

    repository.findOrderRequestWithDetails.resolves(orderRequest)
    repository.findRequestedTransportOrders.resolves([invalidTransportOrder])

    const useCase = new AcceptOrderRequestUseCase(
      stubDataSource(),
      repository,
      createStubInstance(AuthContext),
      createStubInstance(DomainEventEmitter)
    )

    const orderRequestUuid = randomUUID()

    await expect(useCase.execute(orderRequestUuid))
      .rejects.toThrow(OrderRequestValidationError)
  })

  it('successfully accepts order request with valid transport orders', async () => {
    const repository = createStubInstance(AcceptOrderRequestRepository)
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const authContext = createStubInstance(AuthContext)
    const userUuid = generateUserUuid()
    authContext.getUserUuidOrFail.returns(userUuid)

    const contractType = new ContractTypeEntityBuilder()
      .withName(ContractTypeName.PRIVATE)
      .build()
    const contract = new ContractEntityBuilder().build()
    contract.contractType = contractType

    const orderRequest = new OrderRequestEntityBuilder()
      .withStatus(OrderRequestStatus.REQUESTED)
      .withIsRecurring(false)
      .build()
    orderRequest.contract = contract

    // Create valid transport order with all required fields for non-recurring private contract
    const validTransportOrder = new RequestedTransportOrderEntityBuilder()
      .withDate('2024-12-01') // Required date for non-recurring private contract
      .withDaysOfWeek(null) // Should be null for non-recurring
      .withPlanOnHolidays(null) // Should be null for non-recurring
      .withTargetTime(new Time('10:00'))
      .withTargetTimeFlexibilityBeforeInMinutes(15)
      .withTargetTimeFlexibilityAfterInMinutes(15)
      .withPickupAddress(new AddressBuilder()
        .withPlaceName('Pickup Location')
        .withCity('Brussels')
        .withPostalCode('1000')
        .withStreetName('Main Street')
        .withStreetNumber('123')
        .build())
      .withPickupStopDuration(5)
      .withPickupActionDuration(10)
      .withPickupRemarksForDriver('Pickup remarks')
      .withPickupRemarksForPlanner('Pickup planner remarks')
      .withDropOffAddress(new AddressBuilder()
        .withPlaceName('Drop-off Location')
        .withCity('Antwerp')
        .withPostalCode('2000')
        .withStreetName('Second Street')
        .withStreetNumber('456')
        .build())
      .withDropOffStopDuration(5)
      .withDropOffActionDuration(10)
      .withDropOffRemarksForDriver('Drop off remarks')
      .withDropOffRemarksForPlanner('Drop off planner remarks')
      .build()
    validTransportOrder.orderRequest = orderRequest

    repository.findOrderRequestWithDetails.resolves(orderRequest)
    repository.findRequestedTransportOrders.resolves([validTransportOrder])

    const useCase = new AcceptOrderRequestUseCase(
      stubDataSource(),
      repository,
      authContext,
      eventEmitter
    )

    const orderRequestUuid = randomUUID()

    await useCase.execute(orderRequestUuid)

    expect(repository.acceptOrderRequest.getCalls()).toHaveLength(1)
    expect(repository.acceptOrderRequest.getCalls()[0].args).toEqual([
      orderRequestUuid,
      userUuid
    ])
    expect(eventEmitter.emitOne.getCalls()).toHaveLength(1)
    expect(eventEmitter.emitOne.getCalls()[0].args[0]).toBeInstanceOf(OrderRequestAcceptedEvent)
  })
})

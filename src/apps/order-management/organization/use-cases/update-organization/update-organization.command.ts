import { IsNotEmpty, IsObject, IsString, IsUUID, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { IsNullable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { AddressCommand } from '../../../../../utils/address/address-command.js'

export class UpdateOrganizationCommand {
  @ApiProperty({ type: String })
  @IsString()
  @IsNullable()
  externalId: string | null

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ type: String, nullable: true, description: 'An abbreviated name' })
  @IsString()
  @IsNullable()
  abbreviation: string | null

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  crmBusinessUuid: string

  @ApiProperty({ type: String, nullable: true })
  @IsString()
  @IsNullable()
  vatNumber: string | null

  @ApiProperty({ type: AddressCommand })
  @IsObject()
  @Type(() => AddressCommand)
  @ValidateNested()
  address: AddressCommand
}

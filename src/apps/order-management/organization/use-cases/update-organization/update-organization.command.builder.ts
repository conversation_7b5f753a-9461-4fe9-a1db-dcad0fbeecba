import { randomUUID } from 'crypto'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { UpdateOrganizationCommand } from './update-organization.command.js'

export class UpdateOrganizationCommandBuilder {
  private command: UpdateOrganizationCommand

  constructor () {
    this.command = new UpdateOrganizationCommand()
    this.command.externalId = randomUUID()
    this.command.name = 'name'
    this.command.abbreviation = null
    this.command.crmBusinessUuid = randomUUID()
    this.command.vatNumber = null
    this.command.address = new AddressCommandBuilder().build()
  }

  withExternalId (externalId: string): this {
    this.command.externalId = externalId
    return this
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withAbbreviation (abbreviatedName: string): this {
    this.command.abbreviation = abbreviatedName
    return this
  }

  withCrmBusinessUuid (crmBusinessUuid: string): this {
    this.command.crmBusinessUuid = crmBusinessUuid
    return this
  }

  withVatNumber (vatNumber: string): this {
    this.command.vatNumber = vatNumber
    return this
  }

  withAddress (address: AddressCommand): this {
    this.command.address = address
    return this
  }

  build (): UpdateOrganizationCommand {
    return this.command
  }
}

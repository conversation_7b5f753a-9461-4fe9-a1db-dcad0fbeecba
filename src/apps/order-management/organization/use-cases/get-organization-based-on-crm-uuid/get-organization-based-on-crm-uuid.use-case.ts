import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { Organization } from '../../entities/organization.entity.js'
import { CrmClient } from '../../../../../modules/crm/crm.client.js'
import { UpdateOrCreateOrganizationFromCrmCommandBuilder } from '../update-or-create-from-crm/update-or-create-from-crm.command.builder.js'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { UpdateOrCreateOrganizationFromCrmUseCase } from '../update-or-create-from-crm/update-or-create-from-crm.use-case.js'
import { GetOrganizationBasedOnCrmUuidResponse } from './get-organization-based-on-crm-uuid.response.js'

@Injectable()
export class GetOrganizationBasedOnCrmUuidUseCase {
  constructor (
    @InjectRepository(Organization)
    private readonly organizationRepo: Repository<Organization>,
    private readonly crmClient: CrmClient,
    private readonly updateOrCreateOrganizationFromCrmUseCase:
    UpdateOrCreateOrganizationFromCrmUseCase
  ) {}

  async execute (crmBusinessUuid: string): Promise<GetOrganizationBasedOnCrmUuidResponse> {
    const organization = await this.organizationRepo.findOneBy({ crmBusinessUuid })

    if (organization) {
      return new GetOrganizationBasedOnCrmUuidResponse(organization)
    }

    const business = await this.crmClient.getBusinessOrFail(crmBusinessUuid)

    const command = new UpdateOrCreateOrganizationFromCrmCommandBuilder()
      .withName(business.name)
      .withCrmBusinessUuid(business.uuid)
      .withVatNumber(business.vatNumber)
      .withEmail(business.email)
      .withPhone(business.phone)
      .withMobilePhone(business.mobilePhone)
      .withAddress(new AddressCommandBuilder()
        .withStreetName(business.location?.address.streetName ?? null)
        .withStreetNumber(business.location?.address.streetNumber ?? null)
        .withUnit(business.location?.address.unit ?? null)
        .withPostalCode(business.location?.address.postalCode ?? null)
        .withCity(business.location?.address.city ?? null)
        .withCountry(business.location?.address.country ?? null)
        .withCoordinates(new CoordinatesCommandBuilder()
          .withLatitude(business.location?.address.coordinates?.latitude ?? 0)
          .withLongitude(business.location?.address.coordinates?.longitude ?? 0)
          .build())
        .build())
      .build()

    const organizationUuid = await this.updateOrCreateOrganizationFromCrmUseCase.execute(command)

    const organizationCreated = await this.organizationRepo.findOneByOrFail({
      uuid: organizationUuid
    })
    return new GetOrganizationBasedOnCrmUuidResponse(organizationCreated)
  }
}

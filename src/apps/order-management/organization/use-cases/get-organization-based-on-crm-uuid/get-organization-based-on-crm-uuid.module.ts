import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Organization } from '../../entities/organization.entity.js'
import { CrmModule } from '../../../../../modules/crm/crm.module.js'
import { UpdateOrCreateOrganizationFromCrmModule } from '../update-or-create-from-crm/update-or-create-from-crm.module.js'
import { GetOrganizationBasedOnCrmUuidController } from './get-organization-based-on-crm-uuid.controller.js'
import { GetOrganizationBasedOnCrmUuidUseCase } from './get-organization-based-on-crm-uuid.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Organization]),
    CrmModule,
    UpdateOrCreateOrganizationFromCrmModule
  ],
  providers: [GetOrganizationBasedOnCrmUuidUseCase],
  controllers: [GetOrganizationBasedOnCrmUuidController]
})
export class GetOrganizationBasedOnCrmUuidModule {}

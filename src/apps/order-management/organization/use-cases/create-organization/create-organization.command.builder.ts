import { randomUUID } from 'crypto'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { CreateOrganizationCommand } from './create-organization.command.js'

export class CreateOrganizationCommandBuilder {
  private command: CreateOrganizationCommand

  constructor () {
    this.command = new CreateOrganizationCommand()
    this.command.externalId = randomUUID()
    this.command.name = 'name'
    this.command.abbreviation = null
    this.command.crmBusinessUuid = randomUUID()
    this.command.vatNumber = null
    this.command.address = new AddressCommandBuilder().build()
  }

  withExternalId (id: string): this {
    this.command.externalId = id
    return this
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withAbbreviation (abbreviatedName: string): this {
    this.command.abbreviation = abbreviatedName
    return this
  }

  withCrmBusinessUuid (crmBusinessUuid: string): this {
    this.command.crmBusinessUuid = crmBusinessUuid
    return this
  }

  withVatNumber (vatNumber: string): this {
    this.command.vatNumber = vatNumber
    return this
  }

  withAddress (address: AddressCommand): this {
    this.command.address = address
    return this
  }

  build (): CreateOrganizationCommand {
    return this.command
  }
}

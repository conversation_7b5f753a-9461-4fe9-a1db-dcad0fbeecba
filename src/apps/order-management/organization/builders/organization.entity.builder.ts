import { randomUUID } from 'crypto'
import { Organization } from '../entities/organization.entity.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'
import { Address } from '../../../../utils/address/address.js'
import { generateUuid } from '../../../../modules/database-seeder/uuid-generator.js'

export class OrganizationEntityBuilder {
  private readonly organization: Organization = new Organization()

  constructor () {
    this.organization.uuid = randomUUID()
    this.organization.externalId = randomUUID()
    this.organization.abbreviation = null
    this.organization.name = '<PERSON>'
    this.organization.crmBusinessUuid = randomUUID()
    this.organization.remarksForPlanner = null
    this.organization.remarksForDriver = null
    this.organization.vatNumber = null
    this.organization.address = new AddressBuilder().build()
  }

  withUuid (uuid: string): this {
    this.organization.uuid = uuid
    return this
  }

  withExternalId (id: string | null): this {
    this.organization.externalId = id
    return this
  }

  withName (name: string): this {
    this.organization.name = name
    return this
  }

  withAbbreviation (abbreviatedName: string | null): this {
    this.organization.abbreviation = abbreviatedName
    return this
  }

  withCrmBusinessUuid (crmBusinessUuid: string): this {
    this.organization.crmBusinessUuid = crmBusinessUuid
    this.organization.uuid = generateUuid(crmBusinessUuid)
    return this
  }

  withVatNumber (vatNumber: string | null): this {
    this.organization.vatNumber = vatNumber
    return this
  }

  withAddress (address: Address): this {
    this.organization.address = address
    return this
  }

  withDiscountPerKm (discountPerKm: number): this {
    this.organization.discountPerKm = discountPerKm
    return this
  }

  withDiscountPerMin (discountPerMin: number): this {
    this.organization.discountPerMin = discountPerMin
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): this {
    this.organization.remarksForPlanner = remarksForPlanner
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): this {
    this.organization.remarksForDriver = remarksForDriver
    return this
  }

  build (): Organization {
    return this.organization
  }
}

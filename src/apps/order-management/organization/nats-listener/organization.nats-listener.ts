import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { IntegrationEvent } from '../../../../modules/integration-events/integration-event.js'
import { NatsSubscriber } from '../../../../modules/nats/nats-application/subscribers/nats-subscriber.decorator.js'
import { NatsMessageData } from '../../../../modules/nats/nats-application/parameters/nats-message-data.decorator.js'
import { NatsMsgDataJsonPipe } from '../../../../modules/nats/nats-application/parameters/pipes/nats-message-data-json.pipe.js'
import { IntegrationEventType } from '../../../../modules/integration-events/integration-event.type.js'
import { WiseCrmNatsClient } from '../../../../modules/nats/nats-application/clients/nats-crm.client.js'
import { natsSubject } from '../../../../modules/nats/nats-application/nats-subject.js'
import { Address } from '../../../../utils/address/address.js'
import { OnNatsMessage } from '../../../../modules/nats/nats-application/message-handler/on-nats-message.decorator.js'
import { AddressCommandBuilder } from '../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../utils/address/address-command.js'
import { UpdateOrCreateOrganizationFromCrmUseCase } from '../use-cases/update-or-create-from-crm/update-or-create-from-crm.use-case.js'
import { UpdateOrCreateOrganizationFromCrmCommandBuilder } from '../use-cases/update-or-create-from-crm/update-or-create-from-crm.command.builder.js'
import { OrganizationCreatedIntegrationEventData } from './organization-created-data.js'
import { OrganizationUpdatedIntegrationEventData } from './organization-updated-data.js'

const ORGANIZATION_REQUEST_SUBJECT = '{env}.wise-crm.tenant.{tenantUuid}.business.>'

@NatsSubscriber(configService => ({
  client: WiseCrmNatsClient,
  subject: natsSubject(ORGANIZATION_REQUEST_SUBJECT, {
    env: configService.getOrThrow<string>('NODE_ENV'),
    tenantUuid: configService.getOrThrow<string>('CRM_TENANT_UUID')
  })
}))
export class OrganizationNatsSubscriber {
  constructor (
    private updateOrCreateOrganizationUseCase: UpdateOrCreateOrganizationFromCrmUseCase
  ) {}

  @OnNatsMessage()
  handleEvent (
    @NatsMessageData(NatsMsgDataJsonPipe) event: IntegrationEvent
  ): Promise<void> | void {
    switch (event.type) {
      case IntegrationEventType.BUSINESS_CREATED: return this.handleBusinessCreatedEvent(event)
      case IntegrationEventType.BUSINESS_UPDATED: return this.handleBusinessUpdatedEvent(event)
      default: throw new Error(`Unsupported type: ${event.type}`)
    }
  }

  async handleBusinessCreatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as OrganizationCreatedIntegrationEventData

    const createOrganizationCommand = new UpdateOrCreateOrganizationFromCrmCommandBuilder()
      .withName(data.name)
      .withCrmBusinessUuid(data.businessUuid)
      .withVatNumber(data.vatNumber)
      .withAddress(this.buildAddressCommand(data.address))
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .build()

    await this.updateOrCreateOrganizationUseCase.execute(createOrganizationCommand)
  }

  async handleBusinessUpdatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as OrganizationUpdatedIntegrationEventData

    const updateOrganizationCommand = new UpdateOrCreateOrganizationFromCrmCommandBuilder()
      .withName(data.name)
      .withCrmBusinessUuid(data.businessUuid)
      .withVatNumber(data.vatNumber)
      .withAddress(this.buildAddressCommand(data.address))
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .build()

    await this.updateOrCreateOrganizationUseCase.execute(updateOrganizationCommand)
  }

  private buildAddressCommand (addressString: string): AddressCommand {
    const address = JSON.parse(addressString) as Address
    return new AddressCommandBuilder()
      .withStreetName(address.streetName ?? null)
      .withStreetNumber(address.streetNumber ?? null)
      .withUnit(address.unit ?? null)
      .withCity(address.city ?? null)
      .withPostalCode(address.postalCode ?? null)
      .withCountry(address.country ?? null)
      .withCoordinates(new CoordinatesCommandBuilder()
        .withLatitude(address.coordinates?.latitude ?? 0)
        .withLongitude(address.coordinates?.longitude ?? 0)
        .build())
      .build()
  }
}
